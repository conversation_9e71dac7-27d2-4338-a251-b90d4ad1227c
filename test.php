<?php
use Magento\Framework\App\Bootstrap;
require __DIR__ . '/app/bootstrap.php';

$bootstrap = Bootstrap::create(BP, $_SERVER);
$obj = $bootstrap->getObjectManager();

$state = $obj->get('Magento\Framework\App\State');
$state->setAreaCode('adminhtml');

// Assuming you have defined CampaignsCustomerRepositoryInterface
/** @var \MadHat\Campaign\Api\CustomerRepositoryInterface $repository */
$repository = $obj->get('\MadHat\Campaign\Api\CustomerRepositoryInterface');

// Create operation
$campaignsCustomerData = $obj->create('\MadHat\Campaign\Api\Data\CustomerInterface');
$campaignsCustomerData->setCampaignId(1);
$campaignsCustomerData->setCustomerId(1);
$campaignsCustomerData->setCreatedAt(date('Y-m-d H:i:s'));
$campaignsCustomerData->setUpdatedAt(date('Y-m-d H:i:s'));

try {
    $saved = $repository->save($campaignsCustomerData);
    echo "Created CampaignsCustomer with ID: " . $saved->getEntityId() . "\n";

    // Read operation
    $loaded = $repository->get($saved->getEntityId());
    echo "Loaded CampaignsCustomer with ID: " . $loaded->getEntityId() . "\n";

    // Update operation
    $loaded->setCustomerId(2); // Example change
    $repository->save($loaded);
    echo "Updated CampaignsCustomer with ID: " . $loaded->getEntityId() . "\n";

    // Delete operation
    $repository->delete($loaded);
    echo "Deleted CampaignsCustomer with ID: " . $loaded->getEntityId() . "\n";
} catch (\Exception $e) {
    echo "Error occurred: " . $e->getMessage();
}
