#include:
#  - local: 'cicd/templates/deployment_template.yml'
#  - local: 'cicd/templates/environment_template.yml'

stages:
  - build
  - deploy
#  - sonar

build_artifact:
  stage: build
  before_script:
    - echo "This job build artifacts."
  script:
    - tar -zcvf deploy.tar.gz *
  after_script:
    - echo "Build completed."
  artifacts:
    paths:
      - deploy.tar.gz
  only:
    - deploy/prod
    - deploy/stage01
    - deploy/test01

#sonar_scan:
#  stage: sonar
#  before_script:
#    - echo "This job performs Sonar scanning."
#  script:
#    - /bin/sh cicd/scripts/sonar.sh
#  after_script:
#    - echo "Sonar scanning completed."
#  only:
#    - develop

.deployment_template: &deploy_configuration
  stage: deploy
  dependencies:
    - build_artifact
  variables:
    FF_SCRIPT_SECTIONS: 1 #Do not truncate multiline command log output
  timeout: 40m
  script:
    - |
      # Populate known_hosts
      which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )
      eval $(ssh-agent -s)
      mkdir -p ~/.ssh
      touch ~/.ssh/known_hosts
      ssh-keyscan -H $H >> ~/.ssh/known_hosts
      ssh-keyscan -H $REDIS_HOSTNAME >> ~/.ssh/known_hosts
    # Deploy artifacts
    - ssh $U@$H "if [ -d $MAGENTO_ROOT_DIR-release ]; then sudo rm -rf $MAGENTO_ROOT_DIR-release; fi"
    - ssh $U@$H sudo mkdir -pv $MAGENTO_ROOT_DIR-release
    - ssh $U@$H sudo chown gitlab-runner:magento -R $MAGENTO_ROOT_DIR-release
    - ssh $U@$H sudo chmod 0777 -R $MAGENTO_ROOT_DIR-release
    - scp deploy.tar.gz $U@$H:$MAGENTO_ROOT_DIR-release/
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR-release && tar -xf deploy.tar.gz && rm -f deploy.tar.gz"
    - ssh $U@$H "sleep 5"

    # Python venv & Ansible
    - |
      if which python3 > /dev/null; then echo 'python3 is installed, skipping...'; else \
         sudo apt install python3 -y; \
      fi
      if which python3-venv > /dev/null; then echo 'python3-venv is installed, skipping...'; else \
        sudo apt install python3-venv -y; \
      fi
      if which pip > /dev/null; then echo 'pip is installed, skipping...'; else \
        sudo apt install python3-pip -y; \
      fi
      python3 -m venv ppn-ansible-env
      source ppn-ansible-env/bin/activate && pip install ansible
    - |
      if [ -f ppn-ansible-env/bin/activate ]; then source ppn-ansible-env/bin/activate; else \
        echo "Python venv failure" && exit 1; \
      fi
      # DEBUG MH
      # pwd
      # ppn-ansible-env/bin/ansible-playbook --syntax-check ansible/playbook_php.yml
      # ppn-ansible-env/bin/ansible-playbook --syntax-check ansible/debug.yml

    #Install node 20.x LTS
    - |
      ssh $U@$H "if which node > /dev/null; then echo 'node is installed, skipping...'; else \
        curl -fsSL https://deb.nodesource.com/setup_lts.x | sudo -E bash - && sudo apt-get install -y nodejs; \
      fi"

    #Install composer
    - |
      ssh $U@$H "if which composer > /dev/null; then echo 'composer is installed, skipping...'; else \
        sudo php /tmp/composer-setup.php --install-dir=/usr/local/bin --filename=composer
      fi"

    # delete old backups, retain at least 6
    - |
      ssh $U@$H "sudo mkdir -p $MAGENTO_ROOT_DIR.bak && cd $MAGENTO_ROOT_DIR.bak && if [[ `ls | wc -l` -gt 10 ]]; then \
        ls | sort | head -5 | while read -r dirname; do echo removing backup $dirname && sudo rm -rf $dirname; done; \
      fi"
    # backup magento root for emergency restore
    - ssh $U@$H echo Magento root is $MAGENTO_ROOT_DIR
    - ssh $U@$H sudo mkdir -pv $MAGENTO_ROOT_DIR
    - ssh $U@$H "sleep 5"

    #Disable cron
    - ssh $U@$H sudo crontab -l -u magento-cli > /tmp/magento-cli_crontab.bak 2>/dev/null && sudo crontab -r -u magento-cli && echo "Cron disabled." || echo "No cron to disable."

    #Unmount static dirs
    - ssh $U@$H "sleep 5"
    - |
      ssh $U@$H "if mountpoint -q /var/www/magento/pub/static/_cache; then
          sudo fuser -km /var/www/magento/pub/static/_cache;
          sudo umount -v /var/www/magento/pub/static/_cache;
        fi"
    - |
      ssh $U@$H "if mountpoint -q /var/www/magento/var/billing; then
          sudo fuser -km /var/www/magento/var/billing;
          sudo umount -v /var/www/magento/var/billing;
        fi"

    #Stop newrelic
    - ssh $U@$H "sleep 5"
    - ssh $U@$H sudo systemctl stop newrelic-infra.service
    - ssh $U@$H "sleep 5"

    # Create backup dir and move current install dir there
    - |
      ssh $U@$H "sudo mkdir -pv $MAGENTO_ROOT_DIR.bak/$CI_PIPELINE_IID-$CI_JOB_STARTED_AT"
      ssh $U@$H "sudo mv -v $MAGENTO_ROOT_DIR $MAGENTO_ROOT_DIR.bak/$CI_PIPELINE_IID-$CI_JOB_STARTED_AT/ || exit 1"
    # separate backup of conf.php, env.php as multiple failed releases can make them deeply buried in the versioned backup
    - ssh $U@$H sudo mkdir -p "$MAGENTO_ROOT_DIR"-bkup-files
    - |
      ssh $U@$H "if [ -f $MAGENTO_ROOT_DIR/app/etc/env.php ]; then \
          sudo cp -f $MAGENTO_ROOT_DIR/app/etc/env.php $MAGENTO_ROOT_DIR-bkup-files/env.php; \
        fi"
    - ssh $U@$H "if [ -f $MAGENTO_ROOT_DIR/app/etc/config.php ]; then sudo cp -f $MAGENTO_ROOT_DIR/app/etc/config.php $MAGENTO_ROOT_DIR-bkup-files/config.php; fi"
    # Restore env.php to release dir
    - ssh $U@$H "if [ -f $MAGENTO_ROOT_DIR-bkup-files/env.php ]; then sudo cp -f $MAGENTO_ROOT_DIR-bkup-files/env.php $MAGENTO_ROOT_DIR-release/app/etc/env.php; fi"
    - ssh $U@$H "if [ -f $MAGENTO_ROOT_DIR-bkup-files/config.php ]; then sudo cp -f $MAGENTO_ROOT_DIR-bkup-files/config.php $MAGENTO_ROOT_DIR-release/app/etc/config.php; fi"
    #Prep new magento root
    - ssh $U@$H sudo mkdir -pv $MAGENTO_ROOT_DIR
    - |
      ssh $U@$H "if [ -n $(find $MAGENTO_ROOT_DIR/var/log -mindepth 1 2>/dev/null) ]; then
          if [ -d $MAGENTO_ROOT_DIR/var/log ]; then
            sudo mv $MAGENTO_ROOT_DIR/var/log $MAGENTO_ROOT_DIR-release/var/
          fi
        fi"
    - ssh $U@$H sudo mv $MAGENTO_ROOT_DIR-release/* $MAGENTO_ROOT_DIR || exit 1
    - ssh $U@$H "sleep 5"
    - ssh $U@$H sudo rm -rf $MAGENTO_ROOT_DIR-release
    - ssh $U@$H "sleep 5"
    - ssh $U@$H sudo chmod -R 0777 $MAGENTO_ROOT_DIR
    - ssh $U@$H "sleep 5"
    - ssh $U@$H sudo chown gitlab-runner:magento -R $MAGENTO_ROOT_DIR
    - ssh $U@$H "sleep 5"

    # Restore env.php and config.php to new magento root, if not present through preceding step
    - ssh $U@$H "if [[ ! -f $MAGENTO_ROOT_DIR/app/etc/env.php && -f $MAGENTO_ROOT_DIR-bkup-files/env.php ]]; then sudo cp -f $MAGENTO_ROOT_DIR-bkup-files/env.php $MAGENTO_ROOT_DIR/app/etc/env.php; fi"
    - ssh $U@$H "if [[ ! -f $MAGENTO_ROOT_DIR/app/etc/config.php && -f $MAGENTO_ROOT_DIR-bkup-files/config.php ]]; then sudo cp -f $MAGENTO_ROOT_DIR-bkup-files/config.php $MAGENTO_ROOT_DIR/app/etc/config.php; fi"
    - ssh $U@$H "if [ ! -f $MAGENTO_ROOT_DIR/nginx.conf ]; then echo "Cannot find $MAGENTO_ROOT_DIR/nginx.conf" && exit 1; fi"
    # Run composer
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && composer install --no-cache --no-interaction"

    # First time magento is installed, this must be run to generate env.php and conf.php:
    # - |
    #   ssh $U@$H "if [[ ! -f $MAGENTO_ROOT_DIR/app/etc/env.php && ! -f $MAGENTO_ROOT_DIR/app/etc/config.php ]]; then \
    #     cd $MAGENTO_ROOT_DIR && \
    #     sudo php bin/magento setup:install \
    #       --base-url=https://$BASE_URL \
    #       --db-host=$DB_HOST \
    #       --db-name=magento \
    #       --db-user=$DB_USER \
    #       --db-password=$DB_PW \
    #       --admin-firstname=Magento \
    #       --admin-lastname=User \
    #       --admin-email=$MAGE_ADMIN_EMAIL \
    #       --admin-user=admin \
    #       --admin-password=$MAGE_ADMIN_PW \
    #       --language=en_US \
    #       --currency=USD \
    #       --timezone=America/Chicago \
    #       --use-rewrites=1 \
    #       --search-engine=elasticsearch7 \
    #       --elasticsearch-host=$ES_HOST \
    #       --elasticsearch-port=9200 \
    #       ; fi"

    ## If error "Can't run this operation: deployment configuration is absent. Run 'magento setup:config:set --help' for options." this solution helped once:
    # Remove some folders sudo rm -rf generated/* var/cache/* var/page_cache/* var/view_preprocessed/*
    # then truncate 3 DB table (Doubtful table): core_config_data, setup_module, patch
    # then Run setup:install command with cleanup parameter

    ## dir cleanup, uncomment if hard to diagnose errors during release..
    #- ssh $U@$H sudo rm -rfd $MAGENTO_ROOT_DIR/generated/*
    #- ssh $U@$H "if [ -d $MAGENTO_ROOT_DIR/generated/ ]; then sudo rm -rfd $MAGENTO_ROOT_DIR/generated/*; fi"
    #- ssh $U@$H "if [ -d $MAGENTO_ROOT_DIR/var/di ]; then sudo rm -rfd $MAGENTO_ROOT_DIR/var/di/*; fi"
    #- ssh $U@$H "if [ -d $MAGENTO_ROOT_DIR/pub/static/_cache ]; then sudo rm -rfd $MAGENTO_ROOT_DIR/pub/static/_cache/*; fi"
    #- ssh $U@$H "if [ -d $MAGENTO_ROOT_DIR/pub/static/frontend ]; then sudo rm -rfd $MAGENTO_ROOT_DIR/pub/static/frontend/*; fi"
    #- ssh $U@$H "if [ -d $MAGENTO_ROOT_DIR/var/generation ]; then sudo rm -rfd $MAGENTO_ROOT_DIR/var/generation/*; fi"
    #- ssh $U@$H "if [ -d $MAGENTO_ROOT_DIR/var/cache ]; then sudo rm -rfd $MAGENTO_ROOT_DIR/var/cache/*; fi"
    #- ssh $U@$H "if [ -d $MAGENTO_ROOT_DIR/var/page_cache ]; then sudo rm -rfd $MAGENTO_ROOT_DIR/var/page_cache/*; fi"
    #- ssh $U@$H "if [ -d $MAGENTO_ROOT_DIR/var/view_preprocessed ]; then sudo rm -rfd $MAGENTO_ROOT_DIR/var/view_preprocessed/*; fi"

    # Maybe this step is no longer needed?
    - ssh $U@$H sudo chmod -R 0777 $MAGENTO_ROOT_DIR/var
    - ssh $U@$H "sleep 5"

    # install new release
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo php bin/magento maintenance:enable"
    #Spread out the db operations
    - ssh $U@$H "if [ \"\$(hostname)\" = \"ppn-wa01st\" ]; then sleep 30; fi"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo php bin/magento setup:upgrade"
    #- ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo git apply hotfix/checkoutEmail.patch"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo git apply hotfix/ZeroShippingCreditMemoFix.patch"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo git apply hotfix/MirasvitRemoteFeedUrlFix.patch"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo git apply hotfix/CheckoutShippingException.patch"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo git apply hotfix/KlaviyoNewsLetterAdminFix.patch"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo git apply hotfix/KlaviyoLoggerFix.patch"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo php bin/magento setup:di:compile || exit 1"

    #Generate static files only on one node
    #- ssh $U@$H "if [ \"\$(hostname)\" = \"ppn-wa02st\" ]; then cd $MAGENTO_ROOT_DIR && cd app/design/frontend/MadHat/technology/web/tailwind && sudo npm install; fi"
    #- ssh $U@$H "if [ \"\$(hostname)\" = \"ppn-wa02st\" ]; then cd $MAGENTO_ROOT_DIR && cd app/design/frontend/MadHat/technology/web/tailwind && sudo npm run build-prod; fi"
    #- ssh $U@$H "if [ \"\$(hostname)\" = \"ppn-wa02st\" ]; then cd $MAGENTO_ROOT_DIR && sudo php bin/magento setup:static-content:deploy -f --area adminhtml && sudo php bin/magento setup:static-content:deploy -f --theme=MadHat/technology en_US --no-parent; fi"
    ###### Design proposal: only static/_cache will be minio mounted, generate static on both nodes:
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && cd app/design/frontend/MadHat/technology/web/tailwind && sudo npm install"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && cd app/design/frontend/MadHat/technology/web/tailwind && sudo npm run build-prod"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo php bin/magento setup:static-content:deploy -f --area adminhtml && sudo php bin/magento setup:static-content:deploy -f --theme=MadHat/technology en_US --no-parent"

    #Remount _cache dir safely
    - ssh $U@$H "sleep 5"
    - ssh $U@$H "if mountpoint -q /mnt/minio; then
      sudo fuser -kmv /mnt/minio;
      sudo umount -v /mnt/minio;
      fi"
    - |
      ssh $U@$H 'if mountpoint -q /mnt/minio; then
        echo "Attempting to unmount /mnt/minio..."

        MAX_RETRIES=5       # Maximum number of retries
        RETRY_DELAY=5       # Delay between retries in seconds
        ATTEMPT=0           # Initialize attempt counter

        while mountpoint -q /mnt/minio && [ $ATTEMPT -lt $MAX_RETRIES ]; do
          echo "Running fuser and umount (attempt $((ATTEMPT + 1))/$MAX_RETRIES)..."
          sudo fuser -kmv /mnt/minio
          sudo umount -v /mnt/minio

          # Check if unmount succeeded
          if ! mountpoint -q /mnt/minio; then
            echo "Successfully unmounted /mnt/minio."
            exit 0
          fi

          # Increment attempt counter and wait before retrying
          ATTEMPT=$((ATTEMPT + 1))
          echo "Unmount failed, retrying in $RETRY_DELAY seconds..."
          sleep $RETRY_DELAY
        done

        # Final check after retries
        if mountpoint -q /mnt/minio; then
          echo "Error: Failed to unmount /mnt/minio after $MAX_RETRIES attempts."
          exit 1
        fi
      fi'

    - ssh $U@$H "sudo mkdir -p /mnt/minio"
    - ssh $U@$H 'if grep -q "/mnt/minio" /etc/fstab; then sudo mount -v --target /mnt/minio; fi'
    - ssh $U@$H "sudo mkdir -p /var/www/magento/pub/static/_cache"
    - ssh $U@$H 'if grep -q "/var/www/magento/pub/static/_cache" /etc/fstab; then sudo mount -v --target /var/www/magento/pub/static/_cache; fi'
    - ssh $U@$H "sudo mkdir -p /var/www/magento/var/billing"
    - ssh $U@$H 'if grep -q "/var/www/magento/var/billing" /etc/fstab; then sudo mount -v --target /var/www/magento/var/billing; fi'

    #Wrap up on both nodes
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo php bin/magento cache:clean && sudo php bin/magento cache:flush"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo php bin/magento deploy:mode:set production --skip-compilation"
    - ssh $U@$H "cd $MAGENTO_ROOT_DIR && sudo php bin/magento maintenance:disable"

    # Restore $MAGENTO_ROOT_DIR/var/logs here...

    # cp (from node02 : $MAGENTO_ROOT_DIR/var/view_preprocessed/pub/static/) to (on node01 : $MAGENTO_ROOT_DIR/var/view_preprocessed/pub/static/)
    #- ssh $U@$H "if [ \"\$(hostname)\" = \"ppn-wa01st\" ]; then mkdir -p /var/www/magento/var/view_preprocessed/pub/static; fi"
    #- ssh $U@$H "if [ \"\$(hostname)\" = \"ppn-wa02st\" ]; then rsync -avz --no-perms --no-owner --no-t --delete /var/www/magento/var/view_preprocessed/pub/static/ ppn-wa01st.stage01.inkclub.local:/var/www/magento/var/view_preprocessed/pub/static/; fi"

    # Service restart
    - ssh $U@$H sudo chown magento-cli:magento -R $MAGENTO_ROOT_DIR
    - ssh $U@$H sudo systemctl restart php"$PHP_VERSION"-fpm
    - ssh $U@$H sudo systemctl restart nginx
    - ssh $U@$H sudo systemctl start newrelic-infra.service

    #Enable cron
    - ssh $U@$H [[ -f /tmp/magento-cli_crontab.bak ]] && sudo crontab -u magento-cli /tmp/magento-cli_crontab.bak && echo "Cron restored." || echo "No backup found."
    - ssh $U@$H echo "last command here."

.test_template: &test_configuration
  <<: *deploy_configuration
  environment:
    name: test01
    url: $TEST_BASE_URL
  only:
    - deploy/test01

.stage_template: &stage_configuration
  <<: *deploy_configuration
  environment:
    name: stage01
    url: $STAGE_BASE_URL
  only:
    - deploy/stage01

.production_template: &production_configuration
  <<: *deploy_configuration
  environment:
    name: prod
    url: $PROD_BASE_URL
  only:
    - deploy/prod

deploy_test_node_1:
  <<: *test_configuration
  before_script:
    - export H=$TEST1_HOSTNAME
    - export U=$RUNNER_USERNAME
    - export MAGENTO_ROOT_DIR=$TEST_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$TEST_REDIS_HOSTNAME
    - export PHP_VERSION=$TEST_PHP_VERSION
    - export BASE_URL=STEST_BASE_URL
    - export DB_HOST=$TEST_DB_HOST
    - export DB_PW=$TEST_DB_PW
    - export ES_HOST=$TEST_ES_HOST
    - export MAGE_ADMIN_PW=$TEST_MAGE_ADMIN_PW
    - export MAGE_ADMIN_EMAIL=$TEST_MAGE_ADMIN_EMAIL

deploy_test_node_2:
  <<: *test_configuration
  before_script:
    - export H=$TEST2_HOSTNAME
    - export U=$RUNNER_USERNAME
    - export MAGENTO_ROOT_DIR=$TEST_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$TEST_REDIS_HOSTNAME
    - export PHP_VERSION=$TEST_PHP_VERSION
    - export BASE_URL=STEST_BASE_URL
    - export DB_HOST=$TEST_DB_HOST
    - export DB_PW=$TEST_DB_PW
    - export ES_HOST=$TEST_ES_HOST
    - export MAGE_ADMIN_PW=$TEST_MAGE_ADMIN_PW
    - export MAGE_ADMIN_EMAIL=$TEST_MAGE_ADMIN_EMAIL

deploy_stage_node_1:
  <<: *stage_configuration
  before_script:
    - echo "Before script. Hostname:$STAGE1_HOSTNAME Username:$STAGE1_USERNAME"
    - export H=$STAGE1_HOSTNAME
    - export U=$RUNNER_USERNAME
    - export MAGENTO_ROOT_DIR=$STAGE_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$STAGE_REDIS_HOSTNAME
    - export PHP_VERSION=$STAGE_PHP_VERSION
    - export BASE_URL=STAGE_BASE_URL
    - export DB_HOST=$STAGE_DB_HOST
    - export DB_PW=$STAGE_DB_PW
    - export ES_HOST=$STAGE_ES_HOST
    - export MAGE_ADMIN_PW=$STAGE_MAGE_ADMIN_PW
    - export MAGE_EMAIL=$STAGE_MAGE_EMAIL

deploy_stage_node_2:
  <<: *stage_configuration
  before_script:
    - echo "Before script. Hostname:$STAGE2_HOSTNAME Username:$STAGE2_USERNAME"
    - export H=$STAGE2_HOSTNAME
    - export U=$RUNNER_USERNAME
    - export MAGENTO_ROOT_DIR=$STAGE_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$STAGE_REDIS_HOSTNAME
    - export PHP_VERSION=$STAGE_PHP_VERSION
    - export BASE_URL=STAGE_BASE_URL
    - export DB_HOST=$STAGE_DB_HOST
    - export DB_PW=$STAGE_DB_PW
    - export ES_HOST=$STAGE_ES_HOST
    - export MAGE_ADMIN_PW=$STAGE_MAGE_ADMIN_PW
    - export MAGE_EMAIL=$STAGE_MAGE_EMAIL

deploy_prod_node_1:
  <<: *production_configuration
  before_script:
    - export H=$PROD1_HOSTNAME
    - export U=$RUNNER_USERNAME
    - export MAGENTO_ROOT_DIR=$PROD_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$PROD_REDIS_HOSTNAME
    - export PHP_VERSION=$PROD_PHP_VERSION
    - export BASE_URL=$PROD_BASE_URL
    - export DB_HOST=$PROD_DB_HOST
    - export DB_PW=$PROD_DB_PW
    - export ES_HOST=$PROD_ES_HOST
    - export MAGE_ADMIN_PW=$PROD_MAGE_ADMIN_PW
    - export MAGE_EMAIL=$PROD_MAGE_EMAIL

deploy_prod_node_2:
  <<: *production_configuration
  before_script:
    - export H=$PROD2_HOSTNAME
    - export U=$RUNNER_USERNAME
    - export MAGENTO_ROOT_DIR=$PROD_MAGENTO_ROOT_DIR
    - export REDIS_HOSTNAME=$PROD_REDIS_HOSTNAME
    - export PHP_VERSION=$PROD_PHP_VERSION
    - export BASE_URL=$PROD_BASE_URL
    - export DB_HOST=$PROD_DB_HOST
    - export DB_PW=$PROD_DB_PW
    - export ES_HOST=$PROD_ES_HOST
    - export MAGE_ADMIN_PW=$PROD_MAGE_ADMIN_PW
    - export MAGE_EMAIL=$PROD_MAGE_EMAIL
