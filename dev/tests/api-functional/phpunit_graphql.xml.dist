<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * PHPUnit configuration for GraphQL web API functional tests.
 *
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="http://schema.phpunit.de/9.1/phpunit.xsd"
         colors="true"
         columns="max"
         beStrictAboutTestsThatDoNotTestAnything="false"
         bootstrap="./framework/bootstrap.php"
         testSuiteLoaderClass="Magento\TestFramework\ApiSuiteLoader"
         testSuiteLoaderFile="framework/Magento/TestFramework/ApiSuiteLoader.php"
>
    <!-- Test suites definition -->
    <testsuites>
        <testsuite name="Magento GraphQL web API functional tests">
             <file>testsuite/Magento/WebApiTest.php</file>
        </testsuite>
        <testsuite name="Magento GraphQL web API functional tests real suite">
            <directory>testsuite/Magento/GraphQl</directory>
        </testsuite>
    </testsuites>

    <!-- PHP INI settings and constants definition -->
    <php>
        <includePath>./testsuite</includePath>
        <const name="TESTS_INSTALL_CONFIG_FILE" value="config/install-config-mysql.php"/>
        <!-- Local XML post installation configuration file ('.dist' extension will be added, if the specified file doesn't exist) -->
        <const name="TESTS_POST_INSTALL_SETUP_COMMAND_CONFIG_FILE" value="config/post-install-setup-command-config.php"/>
        <const name="TESTS_GLOBAL_CONFIG_FILE" value="config/config-global.php"/>
        <!-- Webserver URL -->
        <const name="TESTS_BASE_URL" value="http://magento.url"/>
        <!-- Webserver API user -->
        <const name="TESTS_WEBSERVICE_USER" value="admin"/>
        <!-- Webserver API key -->
        <const name="TESTS_WEBSERVICE_APIKEY" value="123123q"/>
        <!-- Define if debugger should be started using XDEBUG_SESSION cookie -->
        <const name="TESTS_XDEBUG_ENABLED" value="false"/>
        <!-- Define XDEBUG_SESSION cookie value-->
        <const name="TESTS_XDEBUG_SESSION" value="phpstorm" />

        <ini name="date.timezone" value="America/Los_Angeles"/>

        <!-- Semicolon-separated 'glob' patterns, that match global XML configuration files -->
        <const name="TESTS_GLOBAL_CONFIG_DIR" value="../../../app/etc"/>
        <!-- Whether to cleanup the application before running tests or not -->
        <const name="TESTS_CLEANUP" value="enabled"/>
        <!--Defines if Magento should be installed before tests execution-->
        <const name="TESTS_MAGENTO_INSTALLATION" value="disabled"/>
        <!-- Magento mode for tests execution. Possible values are "default", "developer" and "production". -->
        <const name="TESTS_MAGENTO_MODE" value="default"/>
        <const name="USE_OVERRIDE_CONFIG" value="enabled"/>
    </php>

    <!-- Test listeners -->
    <listeners>
        <listener class="Magento\TestFramework\Event\PhpUnit"/>
    </listeners>
    <extensions>
        <extension class="Qameta\Allure\PHPUnit\AllureExtension">
            <!-- Optional arguments block; omit it if you want to use default values -->
            <arguments>
                <!-- Path to config file (default is config/allure.config.php) -->
                <string>allure/allure.config.php</string>
            </arguments>
        </extension>
    </extensions>
</phpunit>
