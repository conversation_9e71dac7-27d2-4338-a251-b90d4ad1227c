{"test_table": {"column": {"smallint": true, "tinyint": true, "bigint": true, "float": true, "double": true, "decimal": true, "date": true, "timestamp": true, "datetime": true, "longtext": true, "mediumtext": true, "varchar": true, "mediumblob": true, "blob": true, "boolean": true, "varbinary_rename": true}, "index": {"TEST_TABLE_TINYINT_BIGINT": true}, "constraint": {"TEST_TABLE_SMALLINT_BIGINT": true}}}