<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="test_table" resource="default" comment="Test Table">
        <column xsi:type="int" name="page_id" nullable="true" unsigned="true" identity="true"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="page_id"/>
        </constraint>
    </table>
    <table name="dependent" resource="default" comment="Lol">
        <column xsi:type="float" name="page_id_on" nullable="true" unsigned="true"/>
        <constraint xsi:type="foreign" referenceId="FOREIGN" table="dependent" column="page_id_on" referenceColumn="page_id"
                    referenceTable="test_table"/>
    </table>
</schema>
