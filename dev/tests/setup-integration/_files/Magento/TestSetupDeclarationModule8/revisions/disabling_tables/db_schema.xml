<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="reference_table" resource="default">
        <column xsi:type="tinyint" name="tinyint_ref" padding="7" nullable="false" identity="true" unsigned="false"/>
        <column xsi:type="tinyint" name="tinyint_without_padding" default="0" nullable="false" unsigned="false"/>
        <column xsi:type="bigint" name="bigint_without_padding" default="0" nullable="false" unsigned="false"/>
        <column xsi:type="smallint" name="smallint_without_padding" default="0" nullable="false" unsigned="false"/>
        <column xsi:type="int" name="integer_without_padding" default="0" nullable="false" unsigned="false"/>
        <column xsi:type="smallint" name="smallint_with_big_padding" padding="254" default="0" nullable="false"
                unsigned="false"/>
        <constraint xsi:type="primary" referenceId="tinyint_primary">
            <column name="tinyint_ref"/>
        </constraint>
        <index referenceId="COMPLEX_INDEX" indexType="btree">
            <column name="tinyint_without_padding"/>
            <column name="bigint_without_padding"/>
        </index>
    </table>
    <table name="auto_increment_test" resource="default">
        <column xsi:type="int" name="int_auto_increment_with_nullable" identity="true" padding="12" unsigned="true"
                nullable="true"/>
        <column xsi:type="smallint" name="int_disabled_auto_increment" default="0" identity="false" padding="12"
                unsigned="true" nullable="true"/>
        <constraint xsi:type="unique" referenceId="AUTO_INCREMENT_TEST_INT_AUTO_INCREMENT_WITH_NULLABLE">
            <column name="int_auto_increment_with_nullable"/>
        </constraint>
    </table>
</schema>
