<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="reference_test" resource="default">
        <column xsi:type="smallint" identity="true" name="entity_id" padding="3" nullable="true"/>
        <column xsi:type="smallint" identity="true" name="product_id" padding="3" nullable="true"/>
        <index referenceId="ENTITY_ID_INDEX" indexType="btree">
            <column name="entity_id"/>
        </index>
        <constraint xsi:type="unique" referenceId="UNIQUE_PAIR">
            <column name="entity_id"/>
            <column name="product_id"/>
        </constraint>
        <constraint xsi:type="foreign" referenceId="TEST_TABLE_TINYINT_REFERENCE"
                    column="entity_id" table="reference_test"
                    referenceTable="test_table" referenceColumn="smallint" onDelete="NO ACTION"/>
    </table>
    <table name="auto_increment_test" resource="default">
        <column xsi:type="int" name="int_counter" padding="12" unsigned="true"
                nullable="true"/>
    </table>
</schema>
