<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="test_table" resource="default" comment="Test Table">
        <column xsi:type="int" name="page_id" nullable="false" identity="true" />
        <column xsi:type="varchar" name="email" nullable="true" />
        <column xsi:type="varchar" name="title" />
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="page_id" />
            <column name="email" />
        </constraint>
    </table>
</schema>
