<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Magento\TestSetupDeclarationModule3\Setup\Patch\Data;

use Magento\Framework\App\ResourceConnection;
use Magento\Setup\Exception;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchRevertableInterface;
use Magento\Framework\Setup\Patch\PatchVersionInterface;

/**
 * Class InstallData
 * @package Magento\TestSetupDeclarationModule3\Setup
 */
class ZFirstPatch implements
    DataPatchInterface,
    PatchVersionInterface,
    PatchRevertableInterface
{
    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    /**
     * IncrementalSomeIntegerPatch constructor.
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(ResourceConnection $resourceConnection)
    {
        $this->resourceConnection = $resourceConnection;
    }

    /**
     * @return string
     */
    public static function getVersion()
    {
        return '0.0.3';
    }

    /**
     * @return array
     */
    public function getAliases()
    {
        return [];
    }

    public function revert()
    {
    }

    /**
     * @inheritdoc
     */
    public function apply()
    {
        throw new Exception('This patch should be covered by old script!');
    }

    /**
     * @return array
     */
    public static function getDependencies()
    {
        return [];
    }
}
