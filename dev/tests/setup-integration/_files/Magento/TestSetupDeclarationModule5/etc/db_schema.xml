<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="reference_table" resource="default">
        <column xsi:type="tinyint" name="tinyint_ref" padding="7" nullable="false" identity="true" unsigned="false"/>
        <column xsi:type="int" name="some_integer" default="0" nullable="false" unsigned="false"/>
        <column xsi:type="varchar" name="for_patch_testing" comment="For patch testing" />
        <constraint xsi:type="primary" referenceId="tinyint_primary">
            <column name="tinyint_ref"/>
        </constraint>
    </table>
    <table name="test_table" resource="default">
        <!--Columns-->
        <column xsi:type="smallint" identity="true" name="smallint" padding="3" nullable="true"/>
        <column xsi:type="tinyint" name="tinyint" padding="7" nullable="true" unsigned="false"/>
        <column xsi:type="varchar" name="varchar" length="254" nullable="true"/>
        <column xsi:type="varbinary" name="varbinary" default="10101" />
        <constraint xsi:type="foreign" referenceId="TEST_TABLE_TINYINT_REFERENCE_TABLE_TINYINT_REF"
                    column="tinyint" table="test_table"
                    referenceTable="reference_table" referenceColumn="tinyint_ref" onDelete="CASCADE"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="smallint" />
        </constraint>
    </table>
</schema>
