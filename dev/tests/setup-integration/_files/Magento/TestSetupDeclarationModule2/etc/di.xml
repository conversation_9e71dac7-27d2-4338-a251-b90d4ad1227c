<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Setup\Declaration\Schema\Sharding">
        <arguments>
            <argument name="resources" xsi:type="array">
                <item name="default" xsi:type="string">default</item>
                <item name="shard_one" xsi:type="string">shard_one</item>
                <item name="shard_two" xsi:type="string">shard_two</item>
            </argument>
        </arguments>
    </type>
</config>
