<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="store">
        <column name="store_owner_id" xsi:type="smallint" nullable="true" comment="Store Owner Reference" />
        <constraint xsi:type="foreign"
                    name="STORE_STORE_OWNER_ID_STORE_OWNER_OWNER_ID"
                    column="store_owner_id"
                    table="store"
                    referenceTable="store_owner"
                    referenceColumn="owner_id"
                    onDelete="SET NULL" />
    </table>
    <table name="store_owner" comment="Store owner information" engine="innodb" resource="default">
        <column name="owner_id" xsi:type="smallint" nullable="false" unsigned="false" identity="true" />
        <column name="store_owner_name" onCreate="migrateDataFrom(label)" xsi:type="varchar" length="255" comment="Store Owner Name" />
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="owner_id" />
        </constraint>
    </table>
</schema>
