<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="test_table" resource="default">
        <!--Columns-->
        <column xsi:type="smallint" identity="true" name="smallint" padding="3" nullable="true"/>
        <column xsi:type="tinyint" name="tinyint" padding="7" nullable="true" unsigned="false"/>
        <column xsi:type="bigint" name="bigint" default="0" padding="13" nullable="true" unsigned="false"/>
        <column xsi:type="varchar" name="varchar" length="254" nullable="true"/>
        <!--Constraints-->
        <constraint xsi:type="unique" referenceId="TEST_TABLE_UNIQUE">
            <column name="smallint"/>
            <column name="bigint"/>
        </constraint>
        <!--Indexes-->
        <index referenceId="TEST_TABLE_INDEX" indexType="btree">
            <column name="tinyint"/>
            <column name="bigint"/>
        </index>
        <index referenceId="TEST_TABLE_INDEX_VARCHAR" indexType="btree">
            <column name="varchar"/>
        </index>
    </table>
</schema>
