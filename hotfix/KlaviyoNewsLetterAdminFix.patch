diff --git a/vendor/klaviyo/magento2-extension/Helper/Data.php b/vendor/klaviyo/magento2-extension/Helper/Data.php
index 696c52e13..4e79f42f6 100644
--- a/vendor/klaviyo/magento2-extension/Helper/Data.php
+++ b/vendor/klaviyo/magento2-extension/Helper/Data.php
@@ -93,12 +93,13 @@ class Data extends \Magento\Framework\App\Helper\AbstractHelper
      * @param string|null $firstName
      * @param string|null $lastName
      * @param string|null $source
+     * @param mixed|null $storeId
      * @return array|false|null|string
      */
-    public function subscribeEmailToKlaviyoList($email, $firstName = null, $lastName = null)
+    public function subscribeEmailToKlaviyoList($email, $firstName = null, $lastName = null, $storeId = null)
     {
-        $listId = $this->_klaviyoScopeSetting->getNewsletter();
-        $optInSetting = $this->_klaviyoScopeSetting->getOptInSetting();
+        $listId = $this->_klaviyoScopeSetting->getNewsletter($storeId);
+        $optInSetting = $this->_klaviyoScopeSetting->getOptInSetting($storeId);
 
         $properties = [];
         $properties['email'] = $email;
@@ -109,7 +110,7 @@ class Data extends \Magento\Framework\App\Helper\AbstractHelper
             $properties['last_name'] = $lastName;
         }
 
-        $api = new KlaviyoV3Api($this->_klaviyoScopeSetting->getPublicApiKey(), $this->_klaviyoScopeSetting->getPrivateApiKey(), $this->_klaviyoScopeSetting, $this->_klaviyoLogger);
+        $api = new KlaviyoV3Api($this->_klaviyoScopeSetting->getPublicApiKey($storeId), $this->_klaviyoScopeSetting->getPrivateApiKey($storeId), $this->_klaviyoScopeSetting, $this->_klaviyoLogger);
 
         try {
             if ($optInSetting == ScopeSetting::API_SUBSCRIBE) {
@@ -149,12 +150,14 @@ class Data extends \Magento\Framework\App\Helper\AbstractHelper
 
     /**
      * @param string $email
+     * @param mixed $storeId
      * @return array|string|null
      */
-    public function unsubscribeEmailFromKlaviyoList($email)
+    public function unsubscribeEmailFromKlaviyoList($email, $storeId = null)
     {
-        $api = new KlaviyoV3Api($this->_klaviyoScopeSetting->getPublicApiKey(), $this->_klaviyoScopeSetting->getPrivateApiKey(), $this->_klaviyoScopeSetting, $this->_klaviyoLogger);
-        $listId = $this->_klaviyoScopeSetting->getNewsletter();
+        $api = new KlaviyoV3Api($this->_klaviyoScopeSetting->getPublicApiKey($storeId), $this->_klaviyoScopeSetting->getPrivateApiKey($storeId), $this->_klaviyoScopeSetting, $this->_klaviyoLogger);
+        $listId = $this->_klaviyoScopeSetting->getNewsletter($storeId);
+
         try {
             $response = $api->unsubscribeEmailFromKlaviyoList($email, $listId);
         } catch (\Exception $e) {
diff --git a/vendor/klaviyo/magento2-extension/Observer/NewsletterSubscribeObserver.php b/vendor/klaviyo/magento2-extension/Observer/NewsletterSubscribeObserver.php
index 1cdfac90c..053ade3a4 100644
--- a/vendor/klaviyo/magento2-extension/Observer/NewsletterSubscribeObserver.php
+++ b/vendor/klaviyo/magento2-extension/Observer/NewsletterSubscribeObserver.php
@@ -41,12 +41,16 @@ class NewsletterSubscribeObserver implements ObserverInterface
 
     public function execute(Observer $observer)
     {
-        if (!$this->scopeSetting->isEnabled()) {
+        /** @var Subscriber $subscriber */
+        $subscriber = $observer->getDataObject();
+        $storeId = null;
+        if ($subscriber) {
+            $storeId = $subscriber->getStoreId();
+        }
+        if (!$this->scopeSetting->isEnabled($storeId)) {
             return;
         }
 
-        /** @var Subscriber $subscriber */
-        $subscriber = $observer->getDataObject();
         if ($subscriber && ($subscriber->isStatusChanged() || $subscriber->isObjectNew())) {
             $subscriptionStatus = $subscriber->getStatus();
             $customer = $this->getCustomer($subscriber);
@@ -55,13 +59,15 @@ class NewsletterSubscribeObserver implements ObserverInterface
                 $this->helper->subscribeEmailToKlaviyoList(
                     $customer ? $customer->getEmail() : $subscriber->getEmail(),
                     $customer ? $customer->getFirstname() : $subscriber->getFirstname(),
-                    $customer ? $customer->getLastname() : $subscriber->getLastname()
+                    $customer ? $customer->getLastname() : $subscriber->getLastname(),
+                    $storeId
                 );
             }
 
             if ($subscriber->getId() && $subscriptionStatus === Subscriber::STATUS_UNSUBSCRIBED) {
                 $this->helper->unsubscribeEmailFromKlaviyoList(
-                    $customer ? $customer->getEmail() : $subscriber->getEmail()
+                    $customer ? $customer->getEmail() : $subscriber->getEmail(),
+                    $storeId
                 );
             }
         }
