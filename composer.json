{"name": "magento/project-community-edition", "description": "eCommerce Platform for Growth (Community Edition)", "type": "project", "license": ["OSL-3.0", "AFL-3.0"], "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true, "laminas/laminas-dependency-plugin": true, "magento/*": true, "php-http/discovery": true, "cweagans/composer-patches": true}, "preferred-install": "dist", "sort-packages": true}, "version": "2.4.6-p3", "require": {"adyen/module-hyva-checkout": "^1.1", "adyen/module-payment": "^9.18", "adyen/php-api-library": "^27.0", "amasty/cron-scheduler": "^1.0", "amasty/module-admin-actions-log-subscription-package": "^2.2", "amasty/module-cancel-orders-hyva-compatibility": "^1.0", "amasty/module-company-account-hyva": "^1.0", "amasty/module-customer-group-auto-assign-hyva": "^1.0", "amasty/module-elastic-search-hyva-compatibility": "^1.0", "amasty/module-elastic-search-subscription-package-premium": "^1.5", "amasty/module-ga4": "^2.3", "amasty/module-ga4-hyva": "^1.1", "amasty/module-ga4-hyva-checkout": "^1.0", "amasty/module-groupcat-hyva": "^1.0", "amasty/module-hide-price": "^1.7", "amasty/module-hide-price-hyva": "^1.0", "amasty/module-price-history": "^1.6", "amasty/module-price-history-hyva": "^1.2", "amasty/module-quick-order-hyva": "^1.0", "amasty/module-request-quote-hyva": "^1.0", "amasty/module-request-quote-hyva-checkout": "^1.0", "amasty/module-sales-reps-and-dealers-hyva": "^1.0", "amasty/module-ship-restriction-hyva": "^1.0", "amasty/module-shipping-table-rates-hyva": "^1.0", "amasty/module-shipping-table-rates-hyva-checkout": "^1.0", "amasty/module-shipping-table-rates-subscription-pack": "^1.13", "amasty/module-shop-by-base-hyva-compatibility": "^1.0", "amasty/module-shop-by-brand-graphql": "^1.1", "amasty/module-shop-by-brand-hyva-compatibility": "^1.0", "amasty/module-shopby-grouped-options-hyva": "^1.0", "amasty/module-shopby-hyva-compatibility": "^1.1", "amasty/module-shopby-subscription-package-pro": "^3.1", "amasty/module-sorting-hyva-compatibility": "^1.0", "amasty/module-suite-b2b-subscription-package-premium": "^1.6", "amasty/module-xsearch-hyva-compatibility": "^1.1", "bsscommerce/simple-detail-configurable": "^1.5", "cweagans/composer-patches": "^1.7", "dompdf/dompdf": "^3.0", "hyva-themes/magento2-bsscommerce-simple-detail-configurable": "^1.0", "hyva-themes/magento2-cms-tailwind-jit": "^1.1", "hyva-themes/magento2-compat-module-fallback": "^1.1", "hyva-themes/magento2-default-theme": "^1.3", "hyva-themes/magento2-hyva-checkout": "1.2.0", "hyva-themes/magento2-mageworx-xreviewbase": "^1.0", "hyva-themes/magento2-magezon-ninja-menus": "^1.0", "hyva-themes/magento2-payment-icons": "^2.0", "klaviyo/magento2-extension": "^4.3", "magefan/hyva-theme-blog-plus": "^2.0", "magefan/module-blog-plus": "^2.13", "magento/composer-dependency-version-audit-plugin": "~0.1", "magento/composer-root-update-plugin": "~2.0", "magento/product-community-edition": "2.4.6-p10", "magewirephp/magewire": "1.11.1", "mageworx/module-xreview": "^1.3", "mirasvit/module-feed": "*", "mirasvit/module-order-management": "^1.2", "mobiledetect/mobiledetectlib": "^3.74", "trustedshops/easy_integration": "^1.2"}, "autoload": {"exclude-from-classmap": ["**/dev/**", "**/update/**", "**/Test/**"], "files": ["app/etc/NonComposerComponentRegistration.php"], "psr-0": {"": ["app/code/", "generated/code/"]}, "psr-4": {"Magento\\": "app/code/Magento/", "Magento\\Framework\\": "lib/internal/Magento/Framework/", "Magento\\Setup\\": "setup/src/Magento/Setup/"}}, "require-dev": {"allure-framework/allure-phpunit": "^2", "dealerdirect/phpcodesniffer-composer-installer": "^0.7", "dg/bypass-finals": "^1.4", "friendsofphp/php-cs-fixer": "^3.8", "hyva-themes/hyva-ui": "^2.1", "hyva-themes/upgrade-helper-tools": "dev-main", "lusitanian/oauth": "^0.8", "mage2tv/magento-cache-clean": "^1.0", "magento/ece-tools": "^2002.1", "magento/magento-cloud-docker": "^1.3", "magento/magento-coding-standard": "*", "magento/magento2-functional-testing-framework": "^4.3.1", "pdepend/pdepend": "^2.10", "phpmd/phpmd": "^2.12", "phpstan/phpstan": "^1.9", "phpunit/phpunit": "^9.5", "sebastian/comparator": "<=4.0.6", "sebastian/phpcpd": "^6.0", "symfony/finder": "^5.4", "symfony/process": "<=v5.4.23"}, "conflict": {"gene/bluefoot": "*"}, "autoload-dev": {"psr-4": {"Magento\\PhpStan\\": "dev/tests/static/framework/Magento/PhpStan/", "Magento\\Sniffs\\": "dev/tests/static/framework/Magento/Sniffs/", "Magento\\TestFramework\\Inspection\\": "dev/tests/static/framework/Magento/TestFramework/Inspection/", "Magento\\TestFramework\\Utility\\": "dev/tests/static/framework/Magento/TestFramework/Utility/", "Magento\\Tools\\": "dev/tools/Magento/Tools/", "Magento\\Tools\\Sanity\\": "dev/build/publication/sanity/Magento/Tools/Sanity/"}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"magefan-blog-plus-m2": {"type": "composer", "url": "https://magefan.com/repo/I2zGJdPfBkPx1QtTp0O452K2Tt5ckbJR/i0iNbLLXIBQwPAPOPl5z6FXj0gd7lU3p/"}, "mirasvit-oms2": {"type": "composer", "url": "https://65592:<EMAIL>/65592:Q2CGYSDJBR/"}, "bsscommerce": {"type": "composer", "url": "https://composer.bsscommerce.com/community"}, "mageworx_packages": {"type": "composer", "url": "https://packages.mageworx.com/"}, "mirasvit-pfe2": {"type": "composer", "url": "https://64009:<EMAIL>/64009:576A8GKT3X/"}, "amasty": {"type": "composer", "url": "https://composer.amasty.com/community/"}, "private-packagist": {"type": "composer", "url": "https://hyva-themes.repo.packagist.com/3dprima-com/"}, "0": {"type": "composer", "url": "https://repo.magento.com/"}}, "extra": {"magento-force": "override", "composer-exit-on-patch-failure": true, "patches": {"magento/module-customer-import-export": {"ACSD-54007_2.4.6-p1: Undefined array key _scope error on importing customer data": "patches/composer/ACSD-54007_2.4.6-p1.patch"}}}}