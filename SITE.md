*********************************************
Integrations via SITE and RabbitMQ.

Hostname: rmq01st.stage01.inkclub.local
Virtual host: ppn

Account for integration calls between Magento instance and SITE/RabbitMQ, used by Magento, for PPN sites
Username: magento_ppn
Password: WmtB49J3v8UeosH3pPFy

Account for integration calls between Magento instance and SITE/RabbitMQ, used by Magento, for TechnologyOutlet site
Username: magento_techoutlet
Password: n8ATXoD6gBzwPMGJppmd

Developer account with access to management UI and queues:
Username: ppn-dev
Password: Magento!1234

*********************************************
Direct integrations with SITE

Host: https://sitest.inkclub.com
Metadata: https://sitest.inkclub.com/metadata
UI: https://sitest.inkclub.com/ui

SITE account for direct integration (new orders, order status and customer):


Technology Outlet configs
CompanyId: TechOut-Magento
SecretKey: xN9z7ZPr5V3y8MsW2eKkCS


PPN configs
CompanyId: PPN-Magento
SecretKey: x5VbUJXNUSNPSsjCYf3R

