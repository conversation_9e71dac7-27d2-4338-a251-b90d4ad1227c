#PHP
- name: Hold undesired php installables
  dpkg_selections:
    name: "{{ item }}"
    selection: hold
  with_items:
    - apache2
    - apache2-bin
    - apache2-data
    - apache2-utils

- name: Pin php package versions
  template:
    src: etc/apt/preferences.d/php-pin.j2
    dest: /etc/apt/preferences.d/php-pin
    owner: root
    group: root
    mode: '0644'
    backup: no

- name: Add php apt repository
  apt_repository:
    repo: "ppa:ondrej/php"
    state: present

- name: "Install PHP {{ php_version }} and Magento PHP Dependencies"
  apt:
    name: [
      "php{{ php_version }}",
      "php{{ php_version }}-bcmath",
      "php{{ php_version }}-bz2",
      "php{{ php_version }}-curl",
      "php{{ php_version }}-fpm",
      "php{{ php_version }}-dom",
      "php{{ php_version }}-gd",
      "php{{ php_version }}-intl",
      "php{{ php_version }}-mysql",
      "php{{ php_version }}-simplexml",
      "php{{ php_version }}-soap",
      "php{{ php_version }}-xml",
      "php{{ php_version }}-xmlreader",
      "php{{ php_version }}-xmlwriter",
      "php{{ php_version }}-zip"
    ]
    state: present
    update_cache: yes
  register: php_deployed


- name: debug php installables
  debug:
    msg: "{{ php_deployed }}"

- name: remove php apache2 config dir
  ansible.builtin.file:
    path: "/etc/php/{{ php_version }}/apache2"
    state: absent

- name: "Template /etc/php/{{ php_version }}/cli/php.ini"
  ansible.builtin.template:
    src: templates/php_cli.ini.j2
    dest: "/etc/php/{{ php_version }}/cli/php.ini"
    force: yes
    #owner: root
    #group: root
    #mode: '0640'
  when: php_deployed.changed or push_php_config is defined

- name: "Template /etc/php/{{ php_version }}/fpm/php.ini"
  ansible.builtin.template:
    src: templates/php_fpm.ini.j2
    dest: "/etc/php/{{ php_version }}/fpm/php.ini"
    force: yes
    #owner: root
    #group: root
    #mode: '0640'
  when: php_deployed.changed or push_php_config is defined

- name: set php default version through update-alternatives alternative
  community.general.alternatives:
    name: php
    path: "/usr/bin/php{{ php_version }}"

- name: Apply php config files
  ansible.builtin.template:
    src: "{{ item }}.j2"
    dest: "/{{ item }}"
    force: yes
    owner: root
    group: root
    mode: '0644'
  with_items:
    - "etc/php/{{ php_version }}/cli/php.ini"
    - "etc/php/{{ php_version }}/fpm/php.ini"
    - "etc/php/{{ php_version }}/fpm/php-fpm.conf"
    - "etc/php/{{ php_version }}/fpm/pool.d/www.conf"

